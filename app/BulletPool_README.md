# BulletPool System

## Overview

The BulletPool system is an optimization for managing BulletHoming instances in the space shooter game. Instead of constantly creating and destroying bullet objects (which causes garbage collection pressure and performance issues), the pool system reuses bullet objects.

## Architecture

### ObjectPool (Base Class)
- Generic object pooling system that can be used for any type of object
- Manages available and active object arrays
- Handles object lifecycle (get/return)
- Configurable pool sizes (initial and maximum)

### BulletPool (Specialized Class)
- Extends ObjectPool specifically for BulletHoming objects
- Provides specialized reset logic for bullet properties
- Offers convenient `get_bullet()` and `return_bullet()` methods
- Handles bullet initialization after retrieval from pool

### BulletHoming (Modified)
- Added `reset_for_pool()` method to properly reset bullet state
- Added `set_bullet_pool()` method to maintain pool reference
- Modified `destroy()` method to return bullets to pool instead of queue_free()
- Added `bullet_pool` property to track which pool the bullet belongs to

## Usage

### Initialization (in Game.gd)
```gdscript
# Initialize BulletPool for optimized bullet management
bullet_pool = BulletPool.create_global_instance(self)
```

### Getting Bullets (replaces BulletHoming.instance())
```gdscript
# Old way:
var homingBullet = BulletHoming.instance()
homingBullet.start()
add_child(homingBullet)

# New way:
var homingBullet = bullet_pool.get_bullet()
homingBullet.position = startPosition
# Note: bullet is automatically added to scene tree by pool
```

### Returning Bullets (automatic)
Bullets are automatically returned to the pool when their `destroy()` method is called, which happens when:
- Bullet hits an enemy
- Bullet goes off-screen
- Bullet lifetime expires

## Performance Benefits

1. **Reduced Garbage Collection**: Reusing objects instead of creating/destroying them
2. **Faster Object Creation**: Pool objects are pre-instantiated and ready to use
3. **Memory Efficiency**: Controlled memory usage with configurable pool limits
4. **Consistent Performance**: Eliminates GC spikes during intense bullet spawning

## Configuration

The pool is configured with:
- **Initial Size**: 20 bullets (pre-created at startup)
- **Maximum Size**: 150 bullets (pool won't grow beyond this)

These values can be adjusted in `BulletPool.create_global_instance()` call.

## Files Modified

1. **New Files**:
   - `scripts/ObjectPool.gd` - Generic object pool base class
   - `scripts/BulletPool.gd` - Specialized bullet pool class

2. **Modified Files**:
   - `scripts/BulletHoming.gd` - Added pooling support methods
   - `scripts/Game.gd` - Replaced BulletHoming.instance() calls with pool.get_bullet()
   - `scripts/player_wing.gd` - Updated wing bullet spawning to use pool

## Testing

The system includes test scripts:
- `scripts/BulletPoolTest.gd` - Comprehensive pool functionality test
- `scripts/PoolValidation.gd` - Basic validation of class creation

## Compatibility

This system is designed for Godot 3.6 and maintains full backward compatibility with existing bullet behavior while providing performance improvements.

### Godot 3.6 Specific Implementation Notes

- Uses composition instead of inheritance for BulletPool (to avoid cyclic reference issues)
- Removes type hints for compatibility with Godot 3.6 syntax
- Uses `load()` instead of direct class references in static methods
- All `export` variables and Godot 3.6 syntax preserved

## Troubleshooting

If you encounter "cyclic reference" errors:
1. Make sure you're using Godot 3.6, not Godot 4.x
2. Verify that class names are properly registered in project.godot
3. Check that no class references itself directly in static methods

## Performance Testing

To test the performance improvements:
1. Enable the bullet hell mode in game
2. Monitor frame rate during intense bullet spawning
3. Compare memory usage before/after implementation
4. Use the built-in pool statistics: `bullet_pool.get_stats()`
