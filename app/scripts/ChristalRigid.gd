# Optimized Crystal implementation using Node2D instead of RigidBody2D
# Provides same behavior with much better performance

extends Node2D

var crystalType = Global.CrystalType.c5
var crystalValue = 5

var wasInit = false

# Physics simulation variables
var velocity = Vector2.ZERO
var gravity = 100.0  # Pixels per second squared
var initial_scatter_applied = false
var scatter_impulses_remaining = 1

# Cache expensive function calls and frequently accessed values
var cached_player_position: Vector2
var cached_permanent_crystal_magnet: bool
var cached_crystal_value_multiplier: float
var player_position_update_counter = 0
var magnet_check_counter = 0

# Optimization: cache magnet effect check result
var cached_has_magnet_effect = false

# Magnet attraction settings
var push_power = 50.0
var magnet_range = 200.0

# Optimization: reduce frequency of expensive operations
var screen_check_counter = 0

func _on_hit(area):
	if area.has_method("canGetPowerup"):
		apply(area)

func apply(_area):

	if not wasInit:
		return false

	# don't pick up if player is dead or just entering the level
	if not Global.GameScene.isPlayerReady():
		return false

	# Use cached player position for sound
	Global.playSound(SoundManager.MoneySound, cached_player_position, -5, (randf()/2)+1.0)
	Global.GameScene.money+=crystalValue
	Global.GameScene.FlashWorldEnv(0.1)
	Global.GameScene.shakeCamera(0.02)

	Global.GameScene.spawnBonusLabel(position ,"$"+str(crystalValue),2,true,false,0.8,0,randi()%50,true);

	queue_free()

func _ready():
	self.visible = false
	var _c = $"Area2D".connect("area_entered",self,"_on_hit")

func init(cType, doSpreadMore = false):
	if doSpreadMore:
		scatter_impulses_remaining = 3

	crystalType = cType
	crystalValue = Global.crystals[crystalType]["value"]

	# Cache ship specs at initialization
	var specs = ShipSpecs.getSpecs()
	cached_crystal_value_multiplier = specs.crystal_value_multiplier
	cached_permanent_crystal_magnet = specs.permanent_crystal_magnet

	# ship spec multiplier
	crystalValue = ceil(crystalValue * cached_crystal_value_multiplier)

	$AnimatedSprite.animation = Global.crystals[crystalType]["animation"]
	self.visible = true

	# Initialize random gravity variation (equivalent to gravity_scale)
	gravity = gravity * (1.0 + randf() * 0.5)

	# Initialize random scatter power
	push_power = randi() % 20 + 50

	# Initialize cached player position
	cached_player_position = Global.getPlayerPosition()

	wasInit = true

func doPushToPlayer():
	return cached_has_magnet_effect || cached_permanent_crystal_magnet

var _h_scatter = 90
var _v_scatter = 50

func _physics_process(delta):
	if not wasInit:
		return

	# Apply initial scatter impulses (replaces RigidBody2D apply_impulse)
	if not initial_scatter_applied and scatter_impulses_remaining > 0:
		# Apply random scatter velocity (equivalent to apply_impulse)
		var scatter_x = (randf() * _h_scatter - 45)# * 2.0  # Horizontal scatter
		var scatter_y = -(randf() * _v_scatter + 20)# * 2.0  # Upward scatter
		velocity += Vector2(scatter_x, scatter_y)
		scatter_impulses_remaining -= 1

		if scatter_impulses_remaining <= 0:
			initial_scatter_applied = true

	# Apply gravity
	velocity.y += gravity * delta

	# Update player position cache less frequently (every 5th frame)
	player_position_update_counter += 1
	if player_position_update_counter >= 5:
		player_position_update_counter = 0
		cached_player_position = Global.getPlayerPosition()

	# Magnet attraction logic
	if doPushToPlayer():
		var distance = cached_player_position - global_position
		var distance_length = distance.length()

		if distance_length < magnet_range:
			# Apply magnet force (replaces the original magnet logic)
			var magnet_strength = push_power / max(distance_length * 0.01, 1.0)
			var magnet_force = distance.normalized() * magnet_strength * delta
			velocity += magnet_force

	# Apply velocity to position
	global_position += velocity * delta

func _process(_delta):

	if not wasInit:
		return false

	# Update magnet effect cache less frequently (every 10th frame)
	magnet_check_counter += 1
	if magnet_check_counter >= 10:
		magnet_check_counter = 0
		cached_has_magnet_effect = Global.GameScene.hasPlayerEffect(Global.PlayerEffect.CRYSTAL_MAGNET)

	# check if out of screen - reduce frequency (every 5th frame)
	screen_check_counter += 1
	if screen_check_counter >= 5:
		screen_check_counter = 0
		if Global.isOffScreenBottom(position, 200):
			queue_free()


