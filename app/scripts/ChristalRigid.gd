extends RigidBody2D

var crystalType = Global.CrystalType.c5
var crystalValue = 5

var wasInit = false

# Cache expensive function calls and frequently accessed values
var cached_player_position: Vector2
var cached_permanent_crystal_magnet: bool
var cached_crystal_value_multiplier: float
var player_position_update_counter = 0
var magnet_check_counter = 0

func _on_hit(area):
	if area.has_method("canGetPowerup"):
		apply(area)

func apply(_area):

	if not wasInit:
		return false

	# don't pick up if player is dead or just entering the level
	if not Global.GameScene.isPlayerReady():
		return false

	# Use cached player position for sound
	Global.playSound(SoundManager.MoneySound, cached_player_position, -5, (randf()/2)+1.0)
	Global.GameScene.money+=crystalValue
	Global.GameScene.FlashWorldEnv(0.1)
	Global.GameScene.shakeCamera(0.02)

	Global.GameScene.spawnBonusLabel(position ,"$"+str(crystalValue),2,true,false,0.8,0,randi()%50,true);

	queue_free()

func _ready():
	self.visible = false
	var _c = $"Area2D".connect("area_entered",self,"_on_hit")

func init(cType, _doSpreadMore = false):

	if(_doSpreadMore):
		applyImpulseTimes = 3

	crystalType = cType
	crystalValue = Global.crystals[crystalType]["value"]

	# Cache ship specs at initialization
	var specs = ShipSpecs.getSpecs()
	cached_crystal_value_multiplier = specs.crystal_value_multiplier
	cached_permanent_crystal_magnet = specs.permanent_crystal_magnet

	# ship spec multipier
	crystalValue = ceil(crystalValue * cached_crystal_value_multiplier)

	$AnimatedSprite.animation = Global.crystals[crystalType]["animation"]
	self.visible = true

	self.gravity_scale = 1+randf()*0.5;

	# Initialize cached player position
	cached_player_position = Global.getPlayerPosition()

	wasInit = true

var physicsCnt = 0
var applyImpulseTimes = 1

# Optimization: cache magnet effect check result
var cached_has_magnet_effect = false

func doPushToPlayer():
	return cached_has_magnet_effect || cached_permanent_crystal_magnet

var push_power = randi()%20+50

# Optimization: reduce frequency of expensive operations
var screen_check_counter = 0

func _physics_process(_delta):
	if(physicsCnt<applyImpulseTimes):
		self.apply_impulse(Vector2(randf()*16,randf()*16), Vector2(randf()*90-45, -(randf()*50+20)))
		physicsCnt+=1

	# Update player position cache less frequently (every 5th frame)
	player_position_update_counter += 1
	if player_position_update_counter >= 5:
		player_position_update_counter = 0
		cached_player_position = Global.getPlayerPosition()

	var distancex = cached_player_position.x - self.global_position.x
	var distancey = cached_player_position.y - self.global_position.y

	# push toward if have effect
	if(doPushToPlayer() && abs(distancex)<200 && abs(distancey)<200):

		if(distancey<=5):
			distancey = 5

		self.global_position.x = self.global_position.x + (distancex * (push_power/distancey) * _delta)

		# var direction = 1;
		# if(distancex<0):
		# 	direction = -1
		# self.global_position.x = self.global_position.x + (direction * push_power * _delta)

func _process(_delta):

	if not wasInit:
		return false

	# Update magnet effect cache less frequently (every 10th frame)
	magnet_check_counter += 1
	if magnet_check_counter >= 10:
		magnet_check_counter = 0
		cached_has_magnet_effect = Global.GameScene.hasPlayerEffect(Global.PlayerEffect.CRYSTAL_MAGNET)

	# check if out of screen - reduce frequency (every 5th frame)
	screen_check_counter += 1
	if screen_check_counter >= 5:
		screen_check_counter = 0
		if Global.isOffScreenBottom(position, 200):
			queue_free()


