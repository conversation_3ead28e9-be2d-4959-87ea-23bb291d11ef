extends "res://scripts/BulletBase.gd"

var offsetY = 0

func destroy(force = false):

	if isDestroyed:
		return false

	$LaserCollision.disabled = true
	# $LaserCollision.position = Vector2(-100000,-10000)
	isDestroyed = true

	# if we don't force the bullet to be destroyed, we only destroy it if it's not a passthrough bullet
	if(not force):
		if(isPassThrough):
			return false

	if(doCountTowardBulletsOnScreen):
		Global.GameScene.BulletsOnScreen -= 1
		Global.GameScene.BulletsOnScreen = max(0, Global.GameScene.BulletsOnScreen)
	
	$LaserCollision.scale = Vector2(0,0)

	Global.setTimeout(self,0.1,self,"queue_free")

	var tween = Global.createTween(self)
	tween.interpolate_property(self,"scale:y",scale.y,0,0.1, Tween.TRANS_LINEAR, Tween.EASE_OUT)

	var tween2 = Global.createTween(self)
	tween2.interpolate_property(self,"scale:x",scale.x,0,0.1, Tween.TRANS_LINEAR, Tween.EASE_OUT)

	tween.start()
	tween2.start()

func _process(delta):

	var speedScale = 1.1

	scale.y += delta*500*speedScale
	scale.x += delta*2*speedScale
	offsetY+=delta*1000*speedScale

	global_position.x = Global.getPlayerPosition().x-3+randi()%6
	global_position.y = Global.getPlayerPosition().y-offsetY

	# destroy bullet if it goes off screen
	if scale.y > 270 && !isDestroyed:
		destroy(true)
		Global.GameScene.levelConductor.logBullet(true)

