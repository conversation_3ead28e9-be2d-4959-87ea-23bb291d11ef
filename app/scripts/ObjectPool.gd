# Generic Object Pool System
# Manages a pool of reusable objects to reduce garbage collection pressure
# and improve performance by avoiding constant instantiation/destruction

extends Node

class_name ObjectPool

# The scene to instantiate objects from
var scene_resource: PackedScene
# Pool of available objects
var available_objects: Array = []
# Pool of objects currently in use
var active_objects: Array = []
# Maximum pool size to prevent unlimited growth
var max_pool_size: int = 100
# Initial pool size to pre-populate
var initial_pool_size: int = 10
# Parent node to add objects to when retrieved
var parent_node: Node

# Initialize the pool with a scene resource
func initialize(scene: PackedScene, parent: Node, initial_size: int = 10, max_size: int = 100):
	scene_resource = scene
	parent_node = parent
	initial_pool_size = initial_size
	max_pool_size = max_size
	
	# Pre-populate the pool
	for _i in range(initial_pool_size):
		var obj = scene_resource.instance()
		obj.set_process(false)
		obj.set_physics_process(false)
		obj.visible = false
		available_objects.append(obj)

# Get an object from the pool
func get_object():
	var obj
	
	if available_objects.size() > 0:
		# Reuse an existing object
		obj = available_objects.pop_back()
	else:
		# Create a new object if pool is empty and we haven't hit the limit
		if active_objects.size() < max_pool_size:
			obj = scene_resource.instance()
		else:
			# Pool is at capacity, return null or oldest active object
			print("Warning: ObjectPool at capacity, creating new object anyway")
			obj = scene_resource.instance()
	
	# Activate the object
	obj.set_process(true)
	obj.set_physics_process(true)
	obj.visible = true
	
	# Add to parent and track as active
	parent_node.add_child(obj)
	active_objects.append(obj)
	
	return obj

# Return an object to the pool
func return_object(obj):
	if obj == null or not is_instance_valid(obj):
		return
	
	# Remove from active tracking
	var index = active_objects.find(obj)
	if index >= 0:
		active_objects.remove(index)
	
	# Remove from scene tree
	if obj.get_parent():
		obj.get_parent().remove_child(obj)
	
	# Reset object state
	_reset_object(obj)
	
	# Deactivate the object
	obj.set_process(false)
	obj.set_physics_process(false)
	obj.visible = false
	
	# Return to available pool if we have space
	if available_objects.size() < max_pool_size:
		available_objects.append(obj)
	else:
		# Pool is full, destroy the object
		obj.queue_free()

# Reset an object to its default state (override in subclasses)
func _reset_object(obj):
	# Default implementation - subclasses should override this
	# to properly reset their specific object types
	if obj.has_method("reset_for_pool"):
		obj.reset_for_pool()

# Get pool statistics
func get_stats():
	return {
		"available": available_objects.size(),
		"active": active_objects.size(),
		"total": available_objects.size() + active_objects.size()
	}

# Clean up the pool
func cleanup():
	# Free all available objects
	for obj in available_objects:
		if is_instance_valid(obj):
			obj.queue_free()
	available_objects.clear()
	
	# Note: Active objects should be returned to pool naturally
	# or will be cleaned up when their parent nodes are freed
	active_objects.clear()

func _exit_tree():
	cleanup()
