# Test script to validate CrystalPool functionality
# This script can be used to test the crystal pooling system independently

extends Node

# Test the CrystalPool system
func test_crystal_pool():
	print("=== CrystalPool Test Started ===")
	
	# Create a test pool
	var pool = CrystalPool.new()
	var crystal_scene = preload("res://scenes/CrystalRigid.tscn")
	
	# Initialize the pool
	pool.initialize(crystal_scene, self, 10, 50)
	
	print("Pool initialized with 10 initial crystals, max 50")
	print("Initial stats: ", pool.get_stats())
	
	# Test getting crystals from pool
	var crystals = []
	for i in range(15):
		var crystal = pool.get_crystal(Global.CrystalType.c50, false)
		if crystal:
			crystals.append(crystal)
			print("Got crystal ", i, " - Pool stats: ", pool.get_stats())
		else:
			print("Failed to get crystal ", i)
	
	print("Retrieved 15 crystals")
	print("Current stats: ", pool.get_stats())
	
	# Test returning crystals to pool
	for i in range(7):
		if i < crystals.size():
			pool.return_crystal(crystals[i])
			print("Returned crystal ", i, " - Pool stats: ", pool.get_stats())
	
	print("Returned 7 crystals")
	print("Final stats: ", pool.get_stats())
	
	# Test getting crystals again (should reuse returned ones)
	for i in range(5):
		var crystal = pool.get_crystal(Global.CrystalType.c20, true)
		if crystal:
			print("Reused crystal ", i, " - Pool stats: ", pool.get_stats())
	
	print("=== CrystalPool Test Completed ===")

# Performance comparison function
func compare_crystal_performance():
	print("=== Crystal Performance Comparison ===")
	
	var start_time = OS.get_ticks_msec()
	
	# Create multiple crystals to test performance
	var crystals = []
	for i in range(200):
		var crystal_scene = preload("res://scenes/CrystalRigid.tscn")
		var crystal = crystal_scene.instance()
		add_child(crystal)
		crystal.init(Global.CrystalType.c50)
		crystals.append(crystal)
	
	var creation_time = OS.get_ticks_msec() - start_time
	print("Time to create 200 crystals (old way): ", creation_time, "ms")
	
	# Simulate some physics frames
	start_time = OS.get_ticks_msec()
	for i in range(60):  # Simulate 1 second at 60fps
		for crystal in crystals:
			if is_instance_valid(crystal):
				crystal._physics_process(1.0/60.0)
	
	var physics_time = OS.get_ticks_msec() - start_time
	print("Time for 60 physics frames: ", physics_time, "ms")
	
	# Clean up
	for crystal in crystals:
		if is_instance_valid(crystal):
			crystal.queue_free()
	
	print("=== Crystal Performance Test Complete ===")

# Test crystal magnet effect with pool
func test_crystal_magnet_with_pool():
	print("=== Crystal Magnet Pool Test ===")
	
	# Create a crystal pool
	var pool = CrystalPool.new()
	var crystal_scene = preload("res://scenes/CrystalRigid.tscn")
	pool.initialize(crystal_scene, self, 5, 20)
	
	# Create a crystal with magnet effect
	var crystal = pool.get_crystal(Global.CrystalType.c100, false)
	crystal.position = Vector2(400, 300)  # Position it away from player
	
	# Force enable magnet effect for testing
	crystal.cached_has_magnet_effect = true
	crystal.cached_permanent_crystal_magnet = true
	
	print("Crystal created at: ", crystal.position)
	print("Magnet effect enabled: ", crystal.doPushToPlayer())
	print("Magnet power: ", crystal.magnet_power)
	print("Magnet range: ", crystal.magnet_range)
	print("Pool stats: ", pool.get_stats())
	
	# Test for a few seconds
	var test_timer = Timer.new()
	test_timer.wait_time = 3.0
	test_timer.one_shot = true
	test_timer.connect("timeout", self, "_cleanup_crystal_magnet_test", [crystal, pool])
	add_child(test_timer)
	test_timer.start()
	
	print("Watch the crystal - it should move toward the player and glow brighter!")
	print("=== Crystal Magnet Pool Test Running for 3 seconds ===")

func _cleanup_crystal_magnet_test(crystal, pool):
	if is_instance_valid(crystal):
		print("Final crystal position: ", crystal.position)
		pool.return_crystal(crystal)
	print("Final pool stats: ", pool.get_stats())
	print("=== Crystal Magnet Pool Test Complete ===")

# Test heavy crystal spawning (like spawnManyCrystals)
func test_heavy_crystal_spawning():
	print("=== Heavy Crystal Spawning Test ===")
	
	var pool = CrystalPool.new()
	var crystal_scene = preload("res://scenes/CrystalRigid.tscn")
	pool.initialize(crystal_scene, self, 30, 200)
	
	var start_time = OS.get_ticks_msec()
	
	# Simulate spawnManyCrystals with 50 crystals
	var crystals = []
	for i in range(50):
		var crystal_type = [
			Global.CrystalType.c5,
			Global.CrystalType.c10,
			Global.CrystalType.c20,
			Global.CrystalType.c50,
			Global.CrystalType.c100
		][i % 5]
		
		var crystal = pool.get_crystal(crystal_type, true)
		crystal.position = Vector2(100 + (i * 20), 100)
		crystals.append(crystal)
	
	var spawn_time = OS.get_ticks_msec() - start_time
	print("Time to spawn 50 crystals with pool: ", spawn_time, "ms")
	print("Pool stats after spawning: ", pool.get_stats())
	
	# Clean up after a short delay
	var cleanup_timer = Timer.new()
	cleanup_timer.wait_time = 1.0
	cleanup_timer.one_shot = true
	cleanup_timer.connect("timeout", self, "_cleanup_heavy_spawn_test", [crystals, pool])
	add_child(cleanup_timer)
	cleanup_timer.start()

func _cleanup_heavy_spawn_test(crystals, pool):
	for crystal in crystals:
		if is_instance_valid(crystal):
			pool.return_crystal(crystal)
	print("Final pool stats after cleanup: ", pool.get_stats())
	print("=== Heavy Crystal Spawning Test Complete ===")

func _ready():
	# Run the basic test
	call_deferred("test_crystal_pool")

# Call tests with keyboard input
func _input(event):
	if event is InputEventKey and event.pressed:
		if event.scancode == KEY_C:
			test_crystal_pool()
		elif event.scancode == KEY_P:
			compare_crystal_performance()
		elif event.scancode == KEY_M:
			test_crystal_magnet_with_pool()
		elif event.scancode == KEY_H:
			test_heavy_crystal_spawning()
