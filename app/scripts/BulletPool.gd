# Specialized Object Pool for BulletHoming instances
# Provides optimized bullet management with proper state reset

extends ObjectPool

class_name BulletPool

# Override the reset method to properly reset BulletHoming objects
func _reset_object(obj):
	if obj.has_method("reset_for_pool"):
		obj.reset_for_pool()
	else:
		# Fallback reset for BulletHoming objects
		_reset_bullet_homing(obj)

# Specialized reset for BulletHoming objects
func _reset_bullet_homing(bullet):
	# Reset position and transform
	bullet.position = Vector2.ZERO
	bullet.rotation = 0
	bullet.scale = Vector2.ONE
	bullet.modulate = Color(1, 1, 1, 1)
	
	# Reset bullet state variables
	bullet.isDestroyed = false
	bullet.wasBulletRemoved = false
	bullet.doCountTowardBulletsOnScreen = true
	bullet.isPassThrough = false
	bullet.canClash = false
	
	# Reset movement variables
	bullet.velocity = Vector2.ZERO
	bullet.acceleration = Vector2.ZERO
	bullet.target = null
	
	# Reset timers and counters
	bullet.steerTimer = 0
	bullet.destroyTimer = 0
	bullet.current_time_cache = 0
	bullet.target_selection_counter = 0
	bullet.destroy_check_counter = 0
	bullet.bullet_count_timer = 0
	
	# Reset speed to default
	bullet.Speed = Config.BulletSpeed * 0.7
	
	# Reset particle effects
	if bullet.has_node("LightParticle"):
		bullet.get_node("LightParticle").visible = true
		bullet.get_node("LightParticle").emitting = true
	
	# Disconnect any existing signals to prevent issues
	if bullet.is_connected("area_entered", bullet, "_on_hit"):
		bullet.disconnect("area_entered", bullet, "_on_hit")
	
	# Reconnect the hit signal
	if bullet.has_method("_on_hit"):
		var _c = bullet.connect("area_entered", bullet, "_on_hit")

# Get a bullet from the pool and initialize it
func get_bullet(target = null):
	var bullet = get_object()
	if bullet:
		# Set pool reference so bullet can return itself
		bullet.set_bullet_pool(self)
		# Initialize the bullet after getting it from pool
		bullet.start(target)
	return bullet

# Return a bullet to the pool (called from bullet's destroy method)
func return_bullet(bullet):
	return_object(bullet)

# Create a singleton instance for global access
static func create_global_instance(parent_node: Node) -> BulletPool:
	var pool = BulletPool.new()
	var bullet_scene = preload("res://scenes/BulletHoming.tscn")
	pool.initialize(bullet_scene, parent_node, 20, 150)  # Start with 20, max 150
	return pool
